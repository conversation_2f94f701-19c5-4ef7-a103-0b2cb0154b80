import { useState, FC } from "react";
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import reactLogo from "./assets/react.svg";
import viteLogo from "/vite.svg";
import type { AppConfig } from "./types"; 
import { AuthProvider } from "./auth/AuthProvider";
import { LoginButton } from "./components/LoginButton";
import { theme } from './theme';

interface DeploymentInfo {
  readonly buildTime: string;
  readonly environment: string;
}

const DEPLOYMENT_INFO: DeploymentInfo = {
  buildTime: new Date().toISOString(),
  environment: import.meta.env.MODE || "development",
};

const APP_CONFIG: AppConfig = {
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || "https://api.example.com",
  environment: (import.meta.env.MODE as "development" | "staging" | "production") || "development",
  version: "1.0.0",
  features: {
    enableNotifications: true,
    enableReporting: false,
    enableAdvancedFiltering: true,
  },
};

const APP_TITLE = "Scheduling Uplift Frontend - PoC";
const APP_FOOTER_TEXT = "This is a proof of concept for React + Vite + TypeScript deployment to Azure using Bicep and Azure DevOps with Azure AD authentication.";

const AppHeader: FC = () => (
  <Box sx={{ textAlign: 'center', mb: 4 }}>
    <Stack direction="row" spacing={2} justifyContent="center" alignItems="center" sx={{ mb: 2 }}>
      <a href="https://vite.dev" target="_blank" rel="noopener noreferrer">
        <img src={viteLogo} className="logo" alt="Vite logo" />
      </a>
      <a href="https://react.dev" target="_blank" rel="noopener noreferrer">
        <img src={reactLogo} className="logo react" alt="React logo" />
      </a>
    </Stack>
    <Typography variant="h4" component="h1">{APP_TITLE}</Typography>
  </Box>
);

const DeploymentInfoCard: FC<{ info: DeploymentInfo }> = ({ info }) => (
  <Card sx={{ mt: 3 }}>
    <CardContent>
      <Typography variant="h6" component="h3" gutterBottom>
        Deployment Info
      </Typography>
      <Typography variant="body2"><strong>Environment:</strong> {info.environment}</Typography>
      <Typography variant="body2"><strong>Build Time:</strong> {info.buildTime}</Typography>
      <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 1 }}>
        <CheckCircleOutlineIcon color="success" />
        <Typography variant="body2">Successfully deployed to Azure Storage Static Website</Typography>
      </Stack>
    </CardContent>
  </Card>
);

const AppConfigCard: FC<{ config: AppConfig }> = ({ config }) => (
  <Card sx={{ mt: 3 }}>
    <CardContent>
      <Typography variant="h6" component="h3" gutterBottom>
        Application Configuration
      </Typography>
      <Typography variant="body2"><strong>API Base URL:</strong> {config.apiBaseUrl}</Typography>
      <Typography variant="body2"><strong>Version:</strong> {config.version}</Typography>
      <Typography variant="body2" sx={{ mt: 1 }}><strong>Features:</strong></Typography>
      <List dense>
        {Object.entries(config.features).map(([key, value]) => (
          <ListItem key={key} disableGutters>
            {value ? <CheckCircleOutlineIcon color="success" sx={{ mr: 1 }} /> : <HighlightOffIcon color="error" sx={{ mr: 1 }} />}
            <ListItemText primary={key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())} />
          </ListItem>
        ))}
      </List>
    </CardContent>
  </Card>
);

function App() {
  const [count, setCount] = useState(0);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Box sx={{ p: 3, textAlign: 'center', maxWidth: '800px', margin: '0 auto' }}>
          <AppHeader />

          <Box sx={{ my: 4 }}>
            <LoginButton />
          </Box>

          <Card variant="outlined">
            <CardContent>
              <Typography variant="h5" component="p" gutterBottom>
                Count is {count}
              </Typography>
              <Button variant="contained" onClick={() => setCount((c) => c + 1)}>
                Increment Count
              </Button>
              <Typography variant="body2" sx={{ mt: 2 }}>
                Edit <code>src/App.tsx</code> and save to test HMR.
              </Typography>
            </CardContent>
          </Card>

          <DeploymentInfoCard info={DEPLOYMENT_INFO} />
          <AppConfigCard config={APP_CONFIG} />

          <Typography variant="caption" display="block" sx={{ mt: 4, color: 'text.secondary' }}>
            {APP_FOOTER_TEXT}
          </Typography>
        </Box>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;